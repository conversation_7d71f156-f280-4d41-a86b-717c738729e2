
### **软件需求规格说明书 (SRS) - SSH 远程操作工具**

**版本**: 1.3 (修订版)
**日期**: 2025年7月29日
**修订人**: Gemini (AI 需求分析师)

***

### **1. 引言**

#### **1.1 项目目标**
开发一款桌面应用程序，提供简洁、直观的图形用户界面（GUI），用于执行常见的SSH远程操作。核心目标是整合SSH连接管理、脚本执行、文件传输、远程文件编辑及日志查看功能，通过现代化UI设计降低操作复杂性，提升后端开发、运维及数据分析人员的工作效率。

#### **1.2 项目范围**
**范围内功能:**

* SSH连接管理（支持密码和密钥认证，批量导入/导出配置）。
* 远程执行用户输入的脚本或选择服务器上已有脚本执行（支持参数输入）。
* 图形化的本地与远程文件系统浏览。
* 本地与远程之间的文件上传和下载（支持断点续传）。
* 远程文本文件的在线编辑（带备份机制）。
* 实时显示操作和脚本执行日志（支持导出和过滤）。
* 采用Python开发，使用跨平台GUI框架（如PyQt6）。

**范围外功能:**

* 不支持除SSH/SFTP外的协议（如Telnet、FTP、SCP）。
* 不提供完整终端模拟器功能（如vi、top等交互式命令）。
* 不支持多窗口、多标签页会话管理。
* 不涉及复杂计划任务或自动化工作流。
* 不支持代理或跳板机（bastion host）连接。

#### **1.3 目标用户及用例**
* **后端开发工程师**: 快速部署代码到测试服务器，查看日志，执行部署脚本。
* **运维/SRE工程师**: 管理多台服务器，批量执行配置脚本，监控状态，排查故障。
* **数据科学家/分析师**: 上传大型数据集，运行分析脚本，获取结果。

#### **1.4 术语表**
* **SSH**: 安全外壳协议，用于远程服务器安全通信。
* **SFTP**: 安全文件传输协议，基于SSH的文件传输机制。
* **标准输出 (stdout)**: 脚本或命令的正常输出。
* **标准错误 (stderr)**: 脚本或命令的错误输出。
* **GUI**: 图形用户界面。

### **2. 功能需求 (Functional Requirements)**

#### **2.1 FR-1: SSH连接管理 (优先级: 必须)**
* **FR-1.1**: 用户能够创建、保存、编辑、删除SSH连接配置，并支持批量导入/导出配置（JSON/CSV格式）。
* **FR-1.2**: 每个连接配置包含：
    * 连接名称（用户自定义，如“生产Web服务器”）
    * 主机名或IP地址
    * 端口号（默认22）
    * 用户名
    * 认证方式（密码或SSH密钥文件路径）
* **FR-1.3**: 系统安全存储认证信息，密码使用AES-256加密存储于系统钥匙串（如Windows Credential Manager、macOS Keychain）或每次连接时提示输入。SSH密钥文件需验证格式和权限（如600）。
* **FR-1.4**: 用户可从已保存连接列表快速发起连接。
* **FR-1.5**: 界面显示连接状态（未连接、连接中、已连接、连接失败），并在连接失败时提供具体错误原因（如“认证失败”“网络超时”）及操作建议。

#### **2.2 FR-2: 远程脚本执行 (优先级: 必须)**
* **FR-2.1**: 用户可选择以下脚本执行模式：
    * **模式A**: 在UI输入/粘贴脚本并执行。
    * **模式B**: 从远程服务器文件系统选择已有脚本文件执行。
* **FR-2.2 (输入脚本模式)**:
    * 提供多行文本输入区域，支持bash语法高亮。
    * 用户可指定执行目录和脚本运行参数（如 `script.sh --param1 value1`）。
    * 点击“执行”按钮，脚本内容发送至远程服务器在指定目录执行。
    * 支持“保存脚本”按钮，将输入脚本保存为本地或远程文件。
* **FR-2.3 (选择文件模式)**:
    * 提供远程文件系统浏览机制，允许选择脚本文件（如.sh）。
    * 集成于文件管理器（右键菜单“执行此脚本”）或通过“浏览...”按钮。
    * 系统检查脚本文件执行权限（若无权限，提示用户是否添加）。
    * 支持输入脚本运行参数。
* **FR-2.4 (通用执行要求)**:
    * 实时捕获脚本的stdout和stderr，在“执行日志”区域显示，错误信息使用红色高亮。
    * 提供“终止”按钮，中断长时间运行的脚本并显示中断状态。

#### **2.3 FR-3: 文件管理 (优先级: 必须)**
* **FR-3.1 (目录浏览)**:
    * 双栏文件浏览器，左侧显示本地文件系统，右侧显示远程文件系统。
    * 显示文件/文件夹的名称、大小、修改日期和权限。
    * 支持导航到指定路径及文件搜索（支持文件名或正则表达式）。
* **FR-3.2 (文件下载)**:
    * 支持通过拖拽、右键菜单或按钮下载远程文件/文件夹。
    * 支持多文件/文件夹同时下载，显示进度条及断点续传。
* **FR-3.3 (文件上传)**:
    * 支持通过拖拽、右键菜单或按钮上传本地文件/文件夹。
    * 支持多文件/文件夹同时上传，显示进度条及断点续传。
* **FR-3.4 (文件编辑)**:
    * 支持右键远程文本文件（如.txt, .sh, .py, .conf）选择“编辑”。
    * 文件下载至本地临时目录，使用系统默认文本编辑器打开。
    * 保存后自动检测变更并上传覆盖原文件，上传前提示确认并自动备份原文件（如.bak）。
    * 提供恢复备份文件选项。

#### **2.4 FR-4: 日志输出 (优先级: 必须)**
* **FR-4.1**: 提供只读日志显示区域。
* **FR-4.2**: 日志显示以下内容：
    * 连接状态变更（如“正在连接到 192.168.1.1...”, “连接成功”）。
    * 脚本执行的命令原文及stdout/stderr（错误高亮）。
    * 文件传输状态（开始、进度、完成、失败）。
* **FR-4.3**: 每条日志带有时间戳（格式：`[YYYY-MM-DD HH:MM:SS]`）。
* **FR-4.4**: 提供“清空日志”按钮及日志导出功能（保存为.log或.txt）。
* **FR-4.5**: 支持按类型（信息、错误、警告）或时间范围过滤日志，自动滚动至最新行（可关闭）。

### **3. 用户界面 (UI) 详细描述**

#### **3.1 整体布局**
采用三栏式布局，支持动态调整：
* **左侧边栏 (连接管理器)**: 固定宽度（约20%），小屏幕可折叠为可展开菜单。
* **主内容区**: 上下分栏。
    * **上半部分 (文件管理器)**: 占主内容区60%高度。
    * **下半部分 (功能面板)**: 占主内容区40%高度。

#### **3.2 UI组件详细描述**
* **3.2.1 整体风格**
    * **配色**: 浅色模式（背景#F2F2F7，主色调#007AFF，文本#1C1C1E），支持深色模式（背景#1C2526，主色调#40C4FF），随系统主题切换。
    * **字体**: 系统无衬线字体（如San Francisco, Segoe UI）。
    * **圆角**: 容器、按钮、输入框采用8-10px圆角。
    * **图标**: 使用开源Material Icons，确保跨平台一致性。
    * **无障碍**: 支持键盘导航、屏幕阅读器兼容。
* **3.2.2 左侧边栏 (连接管理器)**
    * **标题**: “服务器列表”。
    * **列表项**: 显示服务器图标、连接名称、`user@hostname`及状态指示灯（绿色表示已连接）。
    * **交互**: 单击连接，右键弹出菜单（连接、编辑、删除、导出）。底部“+”按钮添加新连接。
    * **快捷键**: `Ctrl+T`新建连接，`Ctrl+E`编辑连接。
* **3.2.3 主内容区 - 上半部分 (文件管理器)**
    * **导航栏**: 显示面包屑导航路径，支持输入路径跳转。
    * **工具栏**: 包含“上传”“下载”“刷新”“转到目录”“搜索”按钮。
    * **文件视图**: 双栏列表（本地/远程），显示文件属性，支持拖拽、右键菜单（下载、上传、编辑、删除、重命名、执行脚本）。
    * **快捷键**: `Ctrl+R`刷新，`Ctrl+S`搜索。
* **3.2.4 主内容区 - 下半部分 (功能面板)**
    * **选项卡**: iOS分段控件，包含“脚本”和“日志”。
    * **脚本面板**:
        * **模式选择器**: 分段控件（“输入脚本”“选择文件”）。
        * **输入脚本模式**: 多行文本编辑框（支持bash语法高亮），右侧“执行”“保存脚本”按钮，底部参数输入框。
        * **选择文件模式**: 只读文本框显示脚本路径，“浏览...”按钮选择文件，“执行”按钮。
    * **日志面板**:
        * 只读文本区域，显示日志（信息黑色、错误红色、警告黄色）。
        * 支持复制选定日志，右上角“清空”“导出”按钮。
        * 自动滚动至最新行（可关闭）。

### **4. 非功能性需求 (Non-Functional Requirements)**

#### **4.1 NFR-1: 安全性 (优先级: 必须)**
* 使用Paramiko库支持SSHv2协议。
* 凭据使用AES-256加密存储于系统钥匙串。
* 用户输入脚本直接传输至远程执行，不本地解析，避免命令注入。
* 验证SSH密钥文件格式和权限。

#### **4.2 NFR-2: 可用性 (优先级: 必须)**
* UI直观，新用户无需文档即可完成基本操作。
* 可点击元素具有明确的悬停和点击状态。
* 危险操作（如删除、覆盖文件）需二次确认。
* 支持中英文界面，满足国际化需求。
* 支持无障碍功能（键盘导航、屏幕阅读器）。

#### **4.3 NFR-3: 平台兼容性 (优先级: 高)**
* 支持Windows 10/11、macOS 12+、Ubuntu 20.04+、CentOS 8+、Debian 11+。
* 最低要求：4GB内存，2核CPU。
* 打包格式：Windows（.exe）、macOS（.dmg/.app）、Linux（.deb/.rpm）。

#### **4.4 NFR-4: 性能 (优先级: 中)**
* UI响应时间：操作反馈不超过0.2秒。
* SSH连接建立：网络正常时不超过5秒。
* 文件传输速度：至少10MB/s（网络允许时）。
* 文件浏览器刷新：1000个文件目录刷新不超过1秒。
* 支持1GB以上文件传输不崩溃。

### **5. 技术约束**
* **编程语言**: Python 3.9+。
* **SSH/SFTP库**: Paramiko。
* **GUI框架**: PyQt6（推荐，现代化UI）。
* **日志处理**: Python `logging`模块。
* **文件传输**: 基于Paramiko的SFTP协议。
* **加密**: AES-256（凭据存储）。

### **6. 测试用例**

#### **6.1 FR-1: SSH连接管理**
* **TC-1.1**: 创建连接配置，验证保存后可在列表中显示。
* **TC-1.2**: 测试密码加密存储，重启程序后验证仍可解密连接。
* **TC-1.3**: 模拟网络超时，验证错误提示是否明确（如“连接超时”）。

#### **6.2 FR-2: 远程脚本执行**
* **TC-2.1**: 选择无执行权限的脚本文件，验证是否提示添加权限。
* **TC-2.2**: 执行长时间脚本，验证“终止”按钮是否有效。

#### **6.3 FR-3: 文件管理**
* **TC-3.1**: 在包含1000个文件的目录中搜索文件，验证响应时间<1秒。
* **TC-3.2**: 下载1GB文件，模拟网络中断，验证断点续传功能。
* **TC-3.3**: 编辑远程文件，验证保存后自动备份和覆盖原文件。

#### **6.4 FR-4: 日志输出**
* **TC-4.1**: 执行脚本，验证stdout和stderr是否正确显示及高亮。
* **TC-4.2**: 导出日志，验证文件内容是否与界面一致。
* **TC-4.3**: 过滤错误日志，验证是否仅显示红色高亮内容。

### **7. 未来扩展**
* 支持多会话管理（多标签页）。
* 集成云服务实例管理（AWS、GCP）。
* 提供插件机制，允许用户扩展功能（如自定义脚本模板）。

---
