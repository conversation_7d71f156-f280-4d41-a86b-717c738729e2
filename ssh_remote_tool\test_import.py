#!/usr/bin/env python3
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    print("Testing imports...")
    
    print("Importing core modules...")
    from core.credentials_manager import CredentialsManager, load_or_generate_key
    print("✓ credentials_manager imported")
    
    from core.ssh_manager import SSHManager
    print("✓ ssh_manager imported")
    
    from core.file_manager import FileManager
    print("✓ file_manager imported")
    
    from core.script_executor import ScriptExecutor
    print("✓ script_executor imported")
    
    print("Importing UI modules...")
    from ui.connection_manager_widget import ConnectionManagerWidget
    print("✓ connection_manager_widget imported")
    
    from ui.file_browser_widget import FileBrowserWidget
    print("✓ file_browser_widget imported")
    
    from ui.script_panel_widget import ScriptPanelWidget
    print("✓ script_panel_widget imported")
    
    from ui.log_panel_widget import LogPanelWidget
    print("✓ log_panel_widget imported")
    
    from ui.main_window import MainW<PERSON>ow
    print("✓ main_window imported")
    
    print("All imports successful!")
    
except Exception as e:
    print(f"Import error: {e}")
    import traceback
    traceback.print_exc()
